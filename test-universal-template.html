<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通用识别模板测试页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .test-section h2 {
            color: #34495e;
            margin-top: 0;
        }
        .test-input {
            width: 100%;
            height: 150px;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
            margin-bottom: 15px;
        }
        .test-button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
        }
        .test-button:hover {
            background: #2980b9;
        }
        .test-result {
            margin-top: 20px;
            padding: 15px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            border-color: #27ae60;
            background: #d5f4e6;
        }
        .error {
            border-color: #e74c3c;
            background: #fdf2f2;
        }
        .info {
            background: #e8f4fd;
            border-color: #3498db;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 通用识别模板功能测试</h1>
        
        <div class="info">
            <strong>测试说明：</strong><br>
            本页面用于测试通用识别模板功能的各项特性，包括关键词检测、LLM处理、fallback机制等。
        </div>

        <!-- 测试1：基础关键词检测 -->
        <div class="test-section">
            <h2>测试1：基础关键词检测</h2>
            <textarea class="test-input" id="test1Input" placeholder="输入包含基础关键词的订单文本...">
航班：CZ8301
时间：22:20
姓名：张三
接机服务
2人
酒店：吉隆坡希尔顿酒店
联系：60123456789
            </textarea>
            <button class="test-button" onclick="testKeywordDetection()">测试关键词检测</button>
            <button class="test-button" onclick="testUniversalParsing('test1Input')">测试通用解析</button>
            <div id="test1Result" class="test-result"></div>
        </div>

        <!-- 测试2：复杂订单格式 -->
        <div class="test-section">
            <h2>测试2：复杂订单格式</h2>
            <textarea class="test-input" id="test2Input" placeholder="输入复杂格式的订单文本...">
预定人：李四
服务日期：1月28日
航班号：AK5749 到达时间：15:30
服务类型：机场接机
乘客：3大人
入住酒店：Kuala Lumpur Convention Centre Hotel
特殊要求：需要举牌服务
联系电话：60198765432
            </textarea>
            <button class="test-button" onclick="testUniversalParsing('test2Input')">测试通用解析</button>
            <div id="test2Result" class="test-result"></div>
        </div>

        <!-- 测试3：最小信息订单 -->
        <div class="test-section">
            <h2>测试3：最小信息订单（Fallback测试）</h2>
            <textarea class="test-input" id="test3Input" placeholder="输入最少信息的订单文本...">
王五
CZ123
接机
            </textarea>
            <button class="test-button" onclick="testUniversalParsing('test3Input')">测试Fallback机制</button>
            <div id="test3Result" class="test-result"></div>
        </div>

        <!-- 测试4：混合语言订单 -->
        <div class="test-section">
            <h2>测试4：混合语言订单</h2>
            <textarea class="test-input" id="test4Input" placeholder="输入中英文混合的订单文本...">
Customer: John Smith
Flight: MH370 Arrival: 18:45
Date: 2.15
Service: Airport pickup
Hotel: Mandarin Oriental Kuala Lumpur
Passengers: 2 people
Contact: +60123456789
            </textarea>
            <button class="test-button" onclick="testUniversalParsing('test4Input')">测试混合语言</button>
            <div id="test4Result" class="test-result"></div>
        </div>

        <!-- 测试5：OTA类型检测 -->
        <div class="test-section">
            <h2>测试5：OTA类型自动检测</h2>
            <textarea class="test-input" id="test5Input" placeholder="输入用于测试自动检测的订单文本...">
航班：SQ123
时间：14:30
客人：赵六
包车服务
4人
地址：KLCC
电话：60111222333
            </textarea>
            <button class="test-button" onclick="testOtaDetection('test5Input')">测试OTA检测</button>
            <div id="test5Result" class="test-result"></div>
        </div>
    </div>

    <!-- 引入必要的脚本文件 -->
    <script src="core/config.js"></script>
    <script src="core/logger.js"></script>
    <script src="core/prompts.js"></script>
    <script src="services/llm-service.js"></script>
    <script src="services/order-parser.js"></script>

    <script>
        // 初始化测试环境
        let orderParser;
        let llmService;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            try {
                llmService = new LLMService();
                orderParser = new OrderParser(llmService);
                console.log('测试环境初始化成功');
            } catch (error) {
                console.error('测试环境初始化失败:', error);
            }
        });

        /**
         * 测试关键词检测功能
         */
        function testKeywordDetection() {
            const input = document.getElementById('test1Input').value;
            const resultDiv = document.getElementById('test1Result');
            
            try {
                if (!orderParser) {
                    throw new Error('OrderParser未初始化');
                }

                // 测试OTA类型检测
                const detectedType = orderParser.detectOtaType(input);
                const confidence = orderParser.getDetectionConfidence(input, detectedType);
                
                // 测试通用关键词提取
                const extractResult = orderParser.extractUniversalInfo(input);
                
                const result = {
                    detectedOtaType: detectedType,
                    confidence: confidence,
                    extractedInfo: extractResult.extractedInfo,
                    matchedFields: extractResult.matchedFields,
                    hasBasicInfo: extractResult.hasBasicInfo
                };
                
                resultDiv.textContent = JSON.stringify(result, null, 2);
                resultDiv.className = 'test-result success';
                
            } catch (error) {
                resultDiv.textContent = `错误: ${error.message}`;
                resultDiv.className = 'test-result error';
            }
        }

        /**
         * 测试通用模板解析
         */
        async function testUniversalParsing(inputId) {
            const input = document.getElementById(inputId).value;
            const resultDiv = document.getElementById(inputId.replace('Input', 'Result'));
            
            try {
                if (!orderParser) {
                    throw new Error('OrderParser未初始化');
                }

                resultDiv.textContent = '正在处理...';
                resultDiv.className = 'test-result';
                
                // 使用通用模板解析
                const result = await orderParser.parseOrders(input, 'universal');
                
                resultDiv.textContent = JSON.stringify(result, null, 2);
                resultDiv.className = result.success ? 'test-result success' : 'test-result error';
                
            } catch (error) {
                resultDiv.textContent = `错误: ${error.message}`;
                resultDiv.className = 'test-result error';
            }
        }

        /**
         * 测试OTA类型检测
         */
        function testOtaDetection(inputId) {
            const input = document.getElementById(inputId).value;
            const resultDiv = document.getElementById(inputId.replace('Input', 'Result'));
            
            try {
                if (!orderParser) {
                    throw new Error('OrderParser未初始化');
                }

                const detectedType = orderParser.detectOtaType(input);
                const confidence = orderParser.getDetectionConfidence(input, detectedType);
                
                // 测试所有OTA类型的匹配情况
                const allTypes = {};
                for (const [type, config] of Object.entries(SYSTEM_CONFIG.OTA_TYPES)) {
                    if (config.keywordPatterns) {
                        allTypes[type] = orderParser.getDetectionConfidence(input, type);
                    }
                }
                
                const result = {
                    detectedType: detectedType,
                    confidence: confidence,
                    allTypeConfidences: allTypes
                };
                
                resultDiv.textContent = JSON.stringify(result, null, 2);
                resultDiv.className = 'test-result success';
                
            } catch (error) {
                resultDiv.textContent = `错误: ${error.message}`;
                resultDiv.className = 'test-result error';
            }
        }
    </script>
</body>
</html>
